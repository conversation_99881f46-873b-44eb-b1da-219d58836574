package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectVendorItemService interface {
	Create(input *dto.PMOVendorItemCreatePayload) (*models.PMOVendorItem, core.IError)
	Update(id string, input *dto.PMOVendorItemUpdatePayload) (*models.PMOVendorItem, core.IError)
	Find(id string) (*models.PMOVendorItem, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOVendorItemPaginationOptions) (*repository.Pagination[models.PMOVendorItem], core.IError)
	Delete(id string) core.IError

	// PMO Vendor Item Version methods
	VendorItemVersionsPagination(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOVendorItemVersion], core.IError)
	VendorItemVersionsFind(versionID string) (*models.PMOVendorItemVersion, core.IError)
}

type pmoProjectVendorItemService struct {
	ctx core.IContext
}

// PMO Vendor Item methods implementation
func (s pmoProjectVendorItemService) Create(input *dto.PMOVendorItemCreatePayload) (*models.PMOVendorItem, core.IError) {
	vendorItem := &models.PMOVendorItem{
		BaseModel:          models.NewBaseModel(),
		ProjectID:          input.ProjectID,
		VendorName:         input.VendorName,
		ItemName:           input.ItemName,
		ItemDetail:         input.ItemDetail,
		DeliverDurationDay: input.DeliverDurationDay,
		IsTor:              input.IsTor,
		IsImplementation:   input.IsImplementation,
		IsTraining:         input.IsTraining,
		IsUserManual:       input.IsUserManual,
		CreatedByID:        utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:        utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMOVendorItem(s.ctx).Create(vendorItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(vendorItem.ID)
}

func (s pmoProjectVendorItemService) Update(id string, input *dto.PMOVendorItemUpdatePayload) (*models.PMOVendorItem, core.IError) {
	existing, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Check if any field has changed to determine if we need to create a version
	shouldCreateVersion := existing.VendorName != input.VendorName ||
		existing.ItemName != input.ItemName ||
		existing.ItemDetail != input.ItemDetail ||
		existing.DeliverDurationDay != input.DeliverDurationDay ||
		existing.IsTor != input.IsTor ||
		existing.IsImplementation != input.IsImplementation ||
		existing.IsTraining != input.IsTraining ||
		existing.IsUserManual != input.IsUserManual

	// Create version record if there are changes
	if shouldCreateVersion {
		version := &models.PMOVendorItemVersion{
			PMOVendorItem: *existing,
			OriginalID:    existing.ID,
		}

		ierr = repositories.PMOVendorItemVersion(s.ctx).Create(version)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	vendorItem := &models.PMOVendorItem{
		VendorName:         input.VendorName,
		ItemName:           input.ItemName,
		ItemDetail:         input.ItemDetail,
		DeliverDurationDay: input.DeliverDurationDay,
		IsTor:              input.IsTor,
		IsImplementation:   input.IsImplementation,
		IsTraining:         input.IsTraining,
		IsUserManual:       input.IsUserManual,
		UpdatedByID:        utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOVendorItem(s.ctx).Where("id = ?", id).Updates(vendorItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(id)
}

func (s pmoProjectVendorItemService) Find(id string) (*models.PMOVendorItem, core.IError) {
	return repositories.PMOVendorItem(s.ctx,
		repositories.PMOVendorItemWithProject(),
	).FindOne("id = ?", id)
}

func (s pmoProjectVendorItemService) Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOVendorItemPaginationOptions) (*repository.Pagination[models.PMOVendorItem], core.IError) {
	return repositories.PMOVendorItem(s.ctx,
		repositories.PMOVendorItemOrderBy(pageOptions),
		repositories.PMOVendorItemWithProject(),
		repositories.PMOVendorItemByProjectID(projectID),
	).Pagination(pageOptions)
}

func (s pmoProjectVendorItemService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOVendorItem(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

// PMO Vendor Item Version methods implementation
func (s pmoProjectVendorItemService) VendorItemVersionsPagination(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOVendorItemVersion], core.IError) {
	return repositories.PMOVendorItemVersion(s.ctx,
		repositories.PMOVendorItemVersionOrderBy(pageOptions),
	).Pagination(pageOptions)
}

func (s pmoProjectVendorItemService) VendorItemVersionsFind(versionID string) (*models.PMOVendorItemVersion, core.IError) {
	return repositories.PMOVendorItemVersion(s.ctx).FindOne("id = ?", versionID)
}

func NewPMOProjectVendorItemService(ctx core.IContext) IPMOProjectVendorItemService {
	return &pmoProjectVendorItemService{ctx: ctx}
}
