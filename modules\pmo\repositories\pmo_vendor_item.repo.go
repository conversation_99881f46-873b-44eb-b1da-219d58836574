package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Vendor Item Repository
var PMOVendorItem = repository.Make[models.PMOVendorItem]()

func PMOVendorItemOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOVendorItem] {
	return func(c repository.IRepository[models.PMOVendorItem]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOVendorItemWithProject() repository.Option[models.PMOVendorItem] {
	return func(c repository.IRepository[models.PMOVendorItem]) {
		c.Preload("Project")
	}
}

func PMOVendorItemByProjectID(projectID string) repository.Option[models.PMOVendorItem] {
	return func(c repository.IRepository[models.PMOVendorItem]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}
