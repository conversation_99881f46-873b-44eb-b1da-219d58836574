package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Vendor Item Version Repository
var PMOVendorItemVersion = repository.Make[models.PMOVendorItemVersion]()

func PMOVendorItemVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOVendorItemVersion] {
	return func(c repository.IRepository[models.PMOVendorItemVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOVendorItemVersionByOriginalID(originalID string) repository.Option[models.PMOVendorItemVersion] {
	return func(c repository.IRepository[models.PMOVendorItemVersion]) {
		if originalID != "" {
			c.Where("vendor_item_id = ?", originalID)
		}
	}
}
