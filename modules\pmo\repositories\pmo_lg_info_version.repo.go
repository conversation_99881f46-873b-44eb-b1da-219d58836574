package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO LG Info Version Repository
var PMOLGInfoVersion = repository.Make[models.PMOLGInfoVersion]()

func PMOLGInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOLGInfoVersion] {
	return func(c repository.IRepository[models.PMOLGInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOLGInfoVersionByLGInfoID(lgInfoID string) repository.Option[models.PMOLGInfoVersion] {
	return func(c repository.IRepository[models.PMOLGInfoVersion]) {
		if lgInfoID != "" {
			c.Where("lg_info_id = ?", lgInfoID)
		}
	}
}
