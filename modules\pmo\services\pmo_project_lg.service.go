package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectLGService interface {
	Create(input *dto.PMOLGCreatePayload) (*models.PMOLGInfo, core.IError)
	Find(projectID string) (*models.PMOLGInfo, core.IError)
	LGVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOLGInfoVersion], core.IError)
}

type pmoProjectLGService struct {
	ctx core.IContext
}

// PMO LG methods implementation
func (s pmoProjectLGService) Create(input *dto.PMOLGCreatePayload) (*models.PMOLGInfo, core.IError) {
	// Check if LG already exists for this project
	existing, ierr := s.Find(input.ProjectID)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if existing != nil {
		// If exists, update it instead by creating a new version and updating the main record
		return s.update(existing, input)
	}

	lg := &models.PMOLGInfo{
		BaseModel:   models.NewBaseModel(),
		ProjectID:   input.ProjectID,
		Value:       input.Value,
		StartDate:   input.StartDate,
		EndDate:     input.EndDate,
		Fee:         input.Fee,
		Interest:    input.Interest,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOLGInfo(s.ctx).Create(lg)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create initial version
	version := &models.PMOLGInfoVersion{
		PMOLGInfo:  *lg,
		OriginalID: lg.ID,
	}

	ierr = repositories.PMOLGInfoVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(lg.ProjectID)
}

func (s pmoProjectLGService) update(existing *models.PMOLGInfo, input *dto.PMOLGCreatePayload) (*models.PMOLGInfo, core.IError) {
	// Update the main record
	existing.Value = input.Value
	existing.StartDate = input.StartDate
	existing.EndDate = input.EndDate
	existing.Fee = input.Fee
	existing.Interest = input.Interest
	existing.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)
	existing.UpdatedAt = utils.GetCurrentDateTime()

	ierr := repositories.PMOLGInfo(s.ctx).Where("id = ?", existing.ID).Updates(existing)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version from current data before updating
	version := &models.PMOLGInfoVersion{
		PMOLGInfo:  *existing,
		OriginalID: existing.ID,
	}

	ierr = repositories.PMOLGInfoVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(existing.ProjectID)
}

func (s pmoProjectLGService) Find(projectID string) (*models.PMOLGInfo, core.IError) {
	return repositories.PMOLGInfo(s.ctx).FindOne("project_id = ?", projectID)
}

func (s pmoProjectLGService) LGVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOLGInfoVersion], core.IError) {
	// First get the LG info to get the original ID
	lg, ierr := s.Find(projectID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOLGInfoVersion(s.ctx,
		repositories.PMOLGInfoVersionOrderBy(pageOptions),
		repositories.PMOLGInfoVersionByLGInfoID(lg.ID),
	).Pagination(pageOptions)
}

func NewPMOProjectLGService(ctx core.IContext) IPMOProjectLGService {
	return &pmoProjectLGService{ctx: ctx}
}
